package com.djcps.ai.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.djcps.ai.dao.entity.*;
import com.djcps.ai.dao.mapper.TaskResultMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskResultService extends ServiceImpl<TaskResultMapper, TaskResult> {
    private final TaskResultItemService taskResultItemService;
    private final TaskInvokeMethodService taskInvokeMethodService;
    private final UserTaskRunRecordService userTaskRunRecordService;
    private final UserTemplateService templateService;

    /**
     * 分页查询TaskResult
     *
     * @param current   当前页
     * @param size      每页大小
     * @param skey      搜索关键字
     * @param cycleType 周期类型：day, week, month
     * @param bizType
     * @return 分页结果
     */
    public IPage<TaskResult> pageQuery(long current, long size, String skey, String cycleType, String bizType) {
        Page<TaskResult> page = new Page<>(current, size);

        LambdaQueryWrapper<TaskResult> queryWrapper = new LambdaQueryWrapper<>();

        // 添加skey条件
        if (StringUtils.hasText(skey)) {
            queryWrapper.eq(TaskResult::getSkey, skey);
        }
        if (StringUtils.hasText(bizType)) {
            queryWrapper.eq(TaskResult::getBizType, bizType);
        }

        // 添加cycleType条件
        if (StringUtils.hasText(cycleType)) {
            queryWrapper.eq(TaskResult::getCycleType, cycleType);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(TaskResult::getDataDate);

        return page(page, queryWrapper);
    }

    public void removeByTemplateDataDate(String skey, Long taskId, String dateDate, String cycleType) {
        LambdaQueryWrapper<TaskResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(TaskResult::getSkey, skey)
                .eq(TaskResult::getDataDate, dateDate)
                .eq(TaskResult::getCycleType, cycleType)
                .eq(TaskResult::getTaskId, taskId)
        ;
        remove(queryWrapper);
    }

    public Long saveResultItem(TaskResult taskResult, UserTemplate template, String batchNo, String originData) {
        TaskResultItem item = new TaskResultItem();
        item.setResultId(taskResult.getId());
        item.setTaskId(taskResult.getTaskId());
        item.setTemplateId(template.getId());
        item.setOriginData(originData);
        item.setSkey(taskResult.getSkey());
        item.setPrompt(template.getPrompt());
        item.setSubBizType(template.getSubBizType());
        item.setBatchNo(batchNo);
        item.setUpdateTime(new Date());
        taskResultItemService.save(item);
        return item.getId();
    }

    public void updateResultItem(Long taskItemId, String analysisResult) {
        TaskResultItem item = new TaskResultItem();
        item.setId(taskItemId);
        item.setItemResult(analysisResult);
        item.setUpdateTime(new Date());
        taskResultItemService.updateById(item);
    }

    public Map<Long, String> templateOutputMap(Set<Long> templateIds) {
        List<UserTemplate> templates = templateService.listByIds(templateIds);
        Set<Long> methodIds = templates.stream().map(UserTemplate::getMethodId).collect(Collectors.toSet());
        List<TaskInvokeMethod> taskInvokeMethods = taskInvokeMethodService.listByIds(methodIds);
        Map<Long, TaskInvokeMethod> methodMap = taskInvokeMethods.stream().collect(Collectors.toMap(TaskInvokeMethod::getId, v -> v));
        Map<Long, String> templateOutputMap = new HashMap<>();
        for (UserTemplate template : templates) {
            Long methodId = template.getMethodId();
            TaskInvokeMethod method = methodMap.get(methodId);
            String outputScheme = "";
            if (method != null && StrUtil.isNotEmpty(method.getOutputScheme())) {
                outputScheme = method.getOutputScheme();
            }
            templateOutputMap.put(template.getId(), outputScheme);
        }
        return templateOutputMap;
    }

    /**
     * 根据批次号重新分析任务结果
     * 根据已有的origin_data重新调用分析功能，更新item_result
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 重新分析的结果项数量
     */
    public int reanalyzeTaskByBatch(Long taskId, String batchNo) {
        log.info("开始重新分析任务，taskId: {}, batchNo: {}", taskId, batchNo);

        // 获取该批次的所有结果项
        List<TaskResultItem> resultItems = taskResultItemService.getByTaskIdAndBatchNo(taskId, batchNo);
        if (resultItems.isEmpty()) {
            log.warn("任务 {} 批次 {} 没有找到相关的结果项", taskId, batchNo);
            return 0;
        }


        // 创建重新分析的执行记录
        Long runRecordId = userTaskRunRecordService.createRunRecord(taskId, batchNo,
                UserTaskRunRecord.ExecType.REANALYZE);
        TaskInvokeMethod analysisMethod = taskInvokeMethodService.getAnalysisMethod();
        int successCount = 0;
        //templateId,  method中的output_scheme
        Map<Long, String> templateOutputMap = templateOutputMap(resultItems.stream().map(TaskResultItem::getTemplateId).collect(Collectors.toSet()));

        try {
            for (TaskResultItem item : resultItems) {
                try {
                    // 检查是否有原始数据
                    if (item.getOriginData() == null || item.getOriginData().trim().isEmpty()) {
                        log.warn("结果项 {} 没有原始数据，跳过重新分析", item.getId());
                        continue;
                    }

                    // 重新调用分析方法
                    String analysisResult = taskInvokeMethodService.invokeAnalysisMethod(
                            taskId,
                            batchNo,
                            item.getSkey(),
                            analysisMethod,
                            item.getPrompt(),
                            item.getOriginData(),
                            templateOutputMap.getOrDefault(item.getTemplateId(), "")
                    );

                    // 更新分析结果
                    if (analysisResult != null) {
                        updateResultItem(item.getId(), analysisResult);
                        successCount++;
                        log.debug("成功重新分析结果项 {}", item.getId());
                    } else {
                        log.warn("结果项 {} 重新分析返回空结果", item.getId());
                    }

                } catch (Exception e) {
                    log.error("重新分析结果项 {} 失败", item.getId(), e);
                }
            }

            // 更新执行记录为成功状态
            userTaskRunRecordService.updateRecordSuccess(runRecordId, successCount);

            log.info("任务 {} 重新分析完成，成功处理 {} 个结果项，总共 {} 个结果项",
                    taskId, successCount, resultItems.size());
            return successCount;
        } catch (Exception e) {
            log.error("重新分析任务 {} 失败", taskId, e);
            // 更新执行记录为失败状态
            userTaskRunRecordService.updateRecordFailed(runRecordId, e.getMessage());
            throw e;
        }
    }

    /**
     * 获取任务结果详情
     *
     * @param taskId  任务ID
     * @param batchNo 批次号
     * @return 任务结果详情
     */
    public Map<String, Object> getTaskResultDetails(Long taskId, String batchNo) {
        Map<String, Object> result = new HashMap<>();

        // 获取TaskResult列表
        LambdaQueryWrapper<TaskResult> taskResultWrapper = new LambdaQueryWrapper<>();
        taskResultWrapper.eq(TaskResult::getTaskId, taskId)
                .eq(TaskResult::getBatchNo, batchNo)
                .eq(TaskResult::getIsDel, 0);
        List<TaskResult> taskResults = list(taskResultWrapper);

        // 获取TaskResultItem列表
        List<TaskResultItem> taskResultItems = taskResultItemService.getByTaskIdAndBatchNo(taskId, batchNo);

        // 按sub_biz_type分组TaskResultItem
        Map<String, List<TaskResultItem>> itemsBySubBizType = taskResultItems.stream()
                .collect(Collectors.groupingBy(TaskResultItem::getSubBizType));

        result.put("taskResults", taskResults);
        result.put("taskResultItems", taskResultItems);
        result.put("itemsBySubBizType", itemsBySubBizType);

        return result;
    }
}
