package com.djcps.ai.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.djcps.ai.dao.base.BaseEntity;
import lombok.Data;

@Data
@TableName("user_template")
public class UserTemplate extends BaseEntity {
    private String userId;
    private String title;
    private String remark;
    /**
     * 业务类型:交付 delivery,销售 sale,客户服务 customer_service
     */
    private String bizType;
    private String subBizType;
    private String prompt;
    private Long methodId;
}
